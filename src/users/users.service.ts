import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatus } from '../entities/user.entity';
import { Role, RoleName } from '../entities/role.entity';
import { CreateUserDto } from '../dto/user/create-user.dto';
import { UpdateUserDto } from '../dto/user/update-user.dto';
import { UpdateProfileDto } from '../dto/user/update-profile.dto';
import { ChangePasswordDto } from '../dto/user/change-password.dto';
import * as bcrypt from 'bcryptjs';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { UserConstants, UserMessages, UserUtils } from '../common/constants/user.constants';
import {
  SafeUser,
  UserCreationResult,
  UserUpdateResult,
  PasswordChangeResult,
  AvatarUploadResult,
  isValidEmail,
  isValidUserId,
  hasRequiredUserFields
} from '../common/types/user.types';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Role)
    private rolesRepository: Repository<Role>,
  ) {}

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { email },
      relations: [...UserConstants.RELATIONS.BASIC],
    });
  }

  async findById(id: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { user_id: id },
      relations: [...UserConstants.RELATIONS.BASIC],
    });
  }

  async create(createUserDto: CreateUserDto): UserCreationResult {
    // Input validation
    if (!hasRequiredUserFields(createUserDto)) {
      throw new BadRequestException('Missing required user fields');
    }

    if (!isValidEmail(createUserDto.email)) {
      throw new BadRequestException('Invalid email format');
    }

    // Check if user already exists
    await this.validateUserDoesNotExist(createUserDto.email);

    // Validate and get roles
    const roles = await this.validateAndGetRoles(createUserDto.role_ids);

    // Hash password
    const hashedPassword = await this.hashPassword(createUserDto.password);

    // Create user
    const user = this.usersRepository.create({
      ...createUserDto,
      password: hashedPassword,
      status: createUserDto.status || UserStatus.ACTIVE,
      roles,
    });

    return this.usersRepository.save(user);
  }

  /**
   * Validate that user with email doesn't already exist
   */
  private async validateUserDoesNotExist(email: string): Promise<void> {
    const existingUser = await this.findByEmail(email);
    if (existingUser) {
      throw new ConflictException(UserMessages.USER_ALREADY_EXISTS);
    }
  }

  /**
   * Validate role IDs and return roles, or default customer role
   */
  private async validateAndGetRoles(roleIds?: string[]): Promise<Role[]> {
    if (UserUtils.validateRoleIds(roleIds)) {
      const foundRoles = await this.rolesRepository.find({
        where: roleIds!.map(id => ({ role_id: id }))
      });
      if (foundRoles.length !== roleIds!.length) {
        throw new NotFoundException(UserMessages.ROLES_NOT_FOUND);
      }
      return foundRoles;
    } else {
      // Assign default customer role if no roles specified
      const defaultRole = await this.rolesRepository.findOne({
        where: { name: RoleName.CUSTOMER },
      });
      return defaultRole ? [defaultRole] : [];
    }
  }

  /**
   * Hash password using configured rounds
   */
  private async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, UserConstants.PASSWORD_HASH_ROUNDS);
  }

  async validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async updateLastLogin(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      last_login: new Date(),
    });
  }

  async updatePassword(userId: string, newPassword: string): Promise<void> {
    const hashedPassword = await this.hashPassword(newPassword);
    await this.usersRepository.update(userId, {
      password: hashedPassword,
    });
  }

  async setTwoFactorCode(userId: string, code: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_code: code,
      two_factor_next_verification: expiresAt,
      two_factor_temp: null as any,
    });
  }

  async setTempTwoFactorCode(userId: string, secret: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_temp: secret,
      two_factor_enabled: false,
      two_factor_next_verification: expiresAt,
    });
  }

  async clearTempTwoFactorCode(userId:string): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_temp: null as any,
    });
  }

  async clearTwoFactorCode(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_code: null as any,
      two_factor_temp: null as any,
    });
  }

  async setTwoFactorCodeTempReset(userId: string, secret: string, code: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_code: code,
      two_factor_temp: secret,
      two_factor_next_verification: expiresAt,
    });
  }

  async disableTwoFactor(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      two_factor_enabled: false,
      two_factor_code: null as any,
      two_factor_temp: null as any,
    });
  }

  async enableTwoFactor(userId:string, expiresAt: Date): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { user_id: userId } });
    await this.usersRepository.update(userId, {
      two_factor_enabled: true,
      two_factor_next_verification:  expiresAt,
      two_factor_temp: null as any,
      email_verified_at: user?.email_verified_at || new Date(),
    });
  }
  async verifyEmail(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      email_verified_at: new Date(),
    });
  }

  async updateStatus(userId: string, status: UserStatus): Promise<void> {
    await this.usersRepository.update(userId, { status });
  }

  async findAll(query: PaginateQuery): Promise<PaginatedResult<User>> {
    console.log('UsersService: findAll called with query:', JSON.stringify(query, null, 2));

    // Build query builder for custom filtering
    const queryBuilder = this.usersRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'roles')
      .leftJoinAndSelect('user.department', 'department');

    // Apply custom filters
    if (query.filter) {
      // Department filter
      if (query.filter.department) {
        queryBuilder.andWhere('user.department_id = :departmentId', {
          departmentId: query.filter.department
        });
      }

      // Role filter
      if (query.filter.role) {
        queryBuilder.andWhere('roles.role_id = :roleId', {
          roleId: query.filter.role
        });
      }

      // Status filter
      if (query.filter.status) {
        queryBuilder.andWhere('user.status = :status', {
          status: query.filter.status
        });
      }
    }

    const config: PaginateConfig<User> = UserUtils.createPaginationConfig();

    console.log('UsersService: Using config:', JSON.stringify(config, null, 2));

    const result = await paginate(query, queryBuilder, config);
    console.log('UsersService: Raw pagination result:', JSON.stringify(result, null, 2));

    const transformedResult = PaginationTransformer.transform<User>(result);
    console.log('UsersService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));

    return transformedResult;
  }

  async update(userId: string, updateUserDto: UpdateUserDto): UserUpdateResult {
    // Input validation
    if (!isValidUserId(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    if (updateUserDto.email && !isValidEmail(updateUserDto.email)) {
      throw new BadRequestException('Invalid email format');
    }

    const user = await this.validateUserExists(userId);

    // Validate roles if provided
    const roles = await this.validateRolesForUpdate(updateUserDto.role_ids);

    // Hash password if provided
    const hashedPassword = updateUserDto.password
      ? await this.hashPassword(updateUserDto.password)
      : undefined;

    // Prepare update data (excluding role_ids which is handled separately)
    const { role_ids, ...updateDataWithoutRoles } = updateUserDto;
    const updateData: Partial<User> = {
      ...updateDataWithoutRoles,
      ...(hashedPassword && { password: hashedPassword }),
    };

    // Remove sensitive fields that shouldn't be updated directly
    const safeUpdateData = UserUtils.sanitizeUpdateData(updateData);

    // Update basic user fields
    Object.assign(user, safeUpdateData);

    // Update roles if provided
    if (roles) {
      user.roles = roles;
    }

    // Save the user with all updates including relations
    await this.usersRepository.save(user);

    // Return fresh user data with relations
    return this.getUpdatedUser(userId);
  }

  /**
   * Validate that user exists and return it
   */
  private async validateUserExists(userId: string): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException(UserMessages.USER_NOT_FOUND);
    }
    return user;
  }

  /**
   * Validate roles for update operation
   */
  private async validateRolesForUpdate(roleIds?: string[]): Promise<Role[] | undefined> {
    if (!UserUtils.validateRoleIds(roleIds)) {
      return undefined;
    }

    const foundRoles = await this.rolesRepository.find({
      where: roleIds!.map(id => ({ role_id: id }))
    });

    if (foundRoles.length !== roleIds!.length) {
      throw new NotFoundException(UserMessages.ROLES_NOT_FOUND);
    }

    return foundRoles;
  }

  /**
   * Get updated user with error handling
   */
  private async getUpdatedUser(userId: string): Promise<User> {
    const updatedUser = await this.findById(userId);
    if (!updatedUser) {
      throw new NotFoundException(UserMessages.USER_NOT_FOUND_AFTER_UPDATE);
    }
    return updatedUser;
  }

  async delete(userId: string): Promise<void> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.softDelete(userId);
  }

  // Profile-specific methods
  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<User> {
    const user = await this.validateUserExists(userId);

    // Check if email is being changed and if it's already taken
    if (UserUtils.isEmailChanged(user.email, updateProfileDto.email)) {
      await this.validateEmailNotTaken(updateProfileDto.email!);
    }

    await this.usersRepository.update(userId, updateProfileDto);
    return this.getUpdatedUser(userId);
  }

  /**
   * Validate that email is not already taken
   */
  private async validateEmailNotTaken(email: string): Promise<void> {
    const existingUser = await this.findByEmail(email);
    if (existingUser) {
      throw new ConflictException(UserMessages.EMAIL_ALREADY_TAKEN);
    }
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): PasswordChangeResult {
    const user = await this.validateUserExists(userId);

    // Verify current password
    const isCurrentPasswordValid = await this.validatePassword(
      changePasswordDto.current_password,
      user.password
    );
    if (!isCurrentPasswordValid) {
      throw new BadRequestException(UserMessages.CURRENT_PASSWORD_INCORRECT);
    }

    // Check if new password matches confirmation
    if (changePasswordDto.new_password !== changePasswordDto.confirm_password) {
      throw new BadRequestException(UserMessages.PASSWORD_MISMATCH);
    }

    // Update password
    await this.updatePassword(userId, changePasswordDto.new_password);

    return { message: UserMessages.PASSWORD_CHANGED_SUCCESS };
  }

  async uploadAvatar(userId: string, file: Express.Multer.File): AvatarUploadResult {
    console.log('UsersService: uploadAvatar called', { userId, file: file ? file.originalname : 'no file' });

    // Input validation
    if (!isValidUserId(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    await this.validateUserExists(userId);

    if (!file) {
      throw new BadRequestException(UserMessages.NO_FILE_UPLOADED);
    }

    console.log('UsersService: File details', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      filename: file.filename,
      path: file.path
    });

    try {
      // Store the file as base64 in the database for simplicity
      // In production, you'd want to use a file storage service like AWS S3
      const base64Image = UserUtils.createBase64Image(file);

      console.log('UsersService: Updating user with base64 image');
      await this.usersRepository.update(userId, {
        profile_image: base64Image,
      });

      console.log('UsersService: Avatar upload successful');
      return this.getUpdatedUser(userId);
    } catch (error) {
      console.error('UsersService: Error uploading avatar', error);
      throw new BadRequestException(UserMessages.AVATAR_UPLOAD_FAILED);
    }
  }

  async removeAvatar(userId: string): Promise<User> {
    await this.validateUserExists(userId);

    await this.usersRepository.update(userId, {
      profile_image: null as any,
    });

    return this.getUpdatedUser(userId);
  }

  async mailUser(userEmail: string): Promise<{ message: string }> {
    const user = await this.findByEmail(userEmail);
    if (!user) {
      throw new NotFoundException(UserMessages.USER_NOT_FOUND);
    }
    return { message: UserMessages.EMAIL_SENT_SUCCESS };
  }
}
