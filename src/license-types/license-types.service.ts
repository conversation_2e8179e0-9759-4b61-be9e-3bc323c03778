import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LicenseTypes } from '../entities/license-types.entity';
import { CreateLicenseTypeDto } from '../dto/license-types/create-license-type.dto';
import { UpdateLicenseTypeDto } from '../dto/license-types/update-license-type.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class LicenseTypesService {
  constructor(
    @InjectRepository(LicenseTypes)
    private licenseTypesRepository: Repository<LicenseTypes>,
  ) {}

  async findAll(query: PaginateQuery): Promise<PaginatedResult<LicenseTypes>> {
    const config: PaginateConfig<LicenseTypes> = {
      sortableColumns: ['name', 'validity', 'created_at'],
      searchableColumns: ['name', 'description'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      relations: ['creator', 'updater'],
    };

    const result = await paginate(query, this.licenseTypesRepository, config);
    return PaginationTransformer.transform<LicenseTypes>(result);
  }

  async findOne(id: string): Promise<LicenseTypes> {
    const licenseType = await this.licenseTypesRepository.findOne({
      where: { license_type_id: id },
      relations: ['creator', 'updater'],
    });

    if (!licenseType) {
      throw new NotFoundException('License type not found');
    }

    return licenseType;
  }

  async create(createLicenseTypeDto: CreateLicenseTypeDto, userId: string): Promise<LicenseTypes> {
    // Check if license type with same name already exists
    const existingLicenseType = await this.licenseTypesRepository.findOne({
      where: { name: createLicenseTypeDto.name },
    });

    if (existingLicenseType) {
      throw new ConflictException('License type with this name already exists');
    }

    const licenseType = this.licenseTypesRepository.create({
      ...createLicenseTypeDto,
      created_by: userId,
    });

    return this.licenseTypesRepository.save(licenseType);
  }

  async update(id: string, updateLicenseTypeDto: UpdateLicenseTypeDto, userId: string): Promise<LicenseTypes> {
    const licenseType = await this.findOne(id);

    // Check if name is being updated and if it conflicts with existing license type
    if (updateLicenseTypeDto.name && updateLicenseTypeDto.name !== licenseType.name) {
      const existingLicenseType = await this.licenseTypesRepository.findOne({
        where: { name: updateLicenseTypeDto.name },
      });

      if (existingLicenseType) {
        throw new ConflictException('License type with this name already exists');
      }
    }

    await this.licenseTypesRepository.update(id, {
      ...updateLicenseTypeDto,
      updated_by: userId,
    });

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const licenseType = await this.findOne(id);
    await this.licenseTypesRepository.softDelete(id);
  }
}
