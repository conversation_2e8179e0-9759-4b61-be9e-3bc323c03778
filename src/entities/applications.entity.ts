import {
  <PERSON><PERSON>ty,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsInt, IsDateString, Min, Max, Matches } from 'class-validator';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { LicenseCategories } from './license-categories.entity';

export enum ApplicationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  EVALUATION = 'evaluation',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

@Entity('applications')
export class Applications {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  application_id: string;

  @Column({ type: 'varchar', unique: true })
  @IsString()
  application_number: string; // Pattern: ^[A-Z]{2,3}-[0-9]{4}-[0-9]{2,3}$

  @Column({ type: 'uuid' })
  @IsUUID()
  applicant_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  license_category_id: string;

  @Column({
    type: 'varchar',
    default: 'draft',
  })
  status: string;

  @Column({ type: 'int' })
  @IsInt()
  @Min(1)
  @Max(6)
  current_step: number;

  @Column({ type: 'int' })
  @IsInt()
  @Min(0)
  @Max(100)
  progress_percentage: number;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  submitted_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => LicenseCategories)
  @JoinColumn({ name: 'license_category_id' })
  license_category: LicenseCategories;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.application_id) {
      this.application_id = uuidv4();
    }
  }
}
