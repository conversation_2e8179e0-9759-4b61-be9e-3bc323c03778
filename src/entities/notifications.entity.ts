import {
  En<PERSON>ty,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';

export enum NotificationType {
  APPLICATION_STATUS = 'application_status',
  EVALUATION_ASSIGNED = 'evaluation_assigned',
  PAYMENT_DUE = 'payment_due',
  LICENSE_EXPIRY = 'license_expiry',
  SYSTEM_ALERT = 'system_alert',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('notifications')
export class Notifications {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  notification_id: string;

  @Column({ type: 'varchar', length: 36 })
  user_id: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'boolean', default: false })
  is_read: boolean;

  @Column({
    type: 'enum',
    enum: NotificationPriority,
    default: NotificationPriority.MEDIUM,
  })
  priority: NotificationPriority;

  @Column({ type: 'varchar', length: 255, nullable: true })
  related_entity_type?: string;

  @Column({ type: 'uuid', nullable: true })
  related_entity_id?: string;

  @Column({ type: 'text', nullable: true })
  action_url?: string;

  @Column({ type: 'timestamp', nullable: true })
  expires_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'timestamp', nullable: true })
  read_at?: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.notification_id) {
      this.notification_id = uuidv4();
    }
  }
}
