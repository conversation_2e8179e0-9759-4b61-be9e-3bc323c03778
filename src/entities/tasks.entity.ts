import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString, Length } from 'class-validator';
import { User } from './user.entity';

export enum TaskType {
  APPLICATION = 'application',
  COMPLAINT = 'complaint',
  DATA_BREACH = 'data_breach',
  EVALUATION = 'evaluation',
  INSPECTION = 'inspection',
  DOCUMENT_REVIEW = 'document_review',
  COMPLIANCE_CHECK = 'compliance_check',
  FOLLOW_UP = 'follow_up',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('tasks')
export class Task {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  task_id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @IsString()
  @Length(1, 100)
  task_number: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  @Length(1, 255)
  title: string;

  @Column({ type: 'text' })
  @IsString()
  description: string;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.APPLICATION,
  })
  @IsEnum(TaskType)
  task_type: TaskType;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  @IsEnum(TaskStatus)
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM,
  })
  @IsEnum(TaskPriority)
  priority: TaskPriority;

  // 🔗 Polymorphic relationship fields
  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  entity_type?: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsUUID()
  entity_id?: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  assigned_to?: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  assigned_by: string;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  assigned_at?: Date;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  due_date?: Date;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  completed_at?: Date;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  review?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  review_notes?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  completion_notes?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  @IsUUID()
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  // Note: Polymorphic relationships are handled at the application level
  // The entity_type and entity_id fields determine the related entity

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_by' })
  assigner: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.task_id) {
      this.task_id = uuidv4();
    }
  }
}