import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { Contacts } from './contacts.entity';

export enum StakeholderPosition {
  CEO = 'CEO',
  SHAREHOLDER = 'Shareholder',
  AUDITOR = 'Auditor',
  LAWYER = 'Lawyer',
}

@Entity('stakeholders')
export class Stakeholder {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  stakeholder_id: string;

  @Column({ type: 'varchar', length: 36 })
  applicant_id: string;

  @Column({ type: 'varchar', length: 100 })
  first_name: string;

  @Column({ type: 'varchar', length: 100 })
  last_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  middle_name?: string;

  @Column({ type: 'varchar', length: 36 })
  contact_id: string;

  @Column({ type: 'varchar', length: 50 })
  nationality: string;

  @Column({
    type: 'enum',
    enum: StakeholderPosition,
  })
  position: StakeholderPosition;

  @Column({ type: 'varchar', length: 300 })
  profile: string;

  @Column({ type: 'varchar', length: 36 })
  cv_document_id: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'varchar', length: 36 })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'varchar', length: 36, nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants, { onDelete: 'CASCADE'})
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => Contacts, { onDelete: 'SET NULL'})
  @JoinColumn({ name: 'contact_id' })
  contact: Contacts;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.stakeholder_id) {
      this.stakeholder_id = uuidv4();
    }
  }
}
