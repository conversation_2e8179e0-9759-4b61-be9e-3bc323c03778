import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTasksTable1736251800000 implements MigrationInterface {
  name = 'CreateTasksTable1736251800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE tasks (
        task_id VARCHAR(36) PRIMARY KEY,
        task_number VARCHAR(100) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        task_type ENUM('application', 'complaint', 'data_breach', 'evaluation', 'inspection', 'document_review', 'compliance_check', 'follow_up') DEFAULT 'application',
        status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'on_hold') DEFAULT 'pending',
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        assigned_to VARCHAR(36) NULL,
        assigned_by VARCHAR(36) NOT NULL,
        assigned_at TIMESTAMP NULL,
        due_date TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        application_id VARCHAR(36) NULL,
        review_notes TEXT NULL,
        completion_notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP NULL,
        INDEX idx_tasks_assigned_to (assigned_to),
        INDEX idx_tasks_assigned_by (assigned_by),
        INDEX idx_tasks_status (status),
        INDEX idx_tasks_task_type (task_type),
        INDEX idx_tasks_application_id (application_id),
        INDEX idx_tasks_created_at (created_at),
        CONSTRAINT fk_tasks_assigned_to FOREIGN KEY (assigned_to) REFERENCES users (user_id),
        CONSTRAINT fk_tasks_assigned_by FOREIGN KEY (assigned_by) REFERENCES users (user_id),
        CONSTRAINT fk_tasks_application_id FOREIGN KEY (application_id) REFERENCES applications (application_id)
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE tasks`);
  }
}