import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService, JwtPayload } from '../auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private authService: AuthService,
    private configService: ConfigService,
  ) {
    const secret = configService.get<string>('JWT_SECRET') || 'macra_jwt_secret_key_change_in_production';
    console.log('🔑 JWT Strategy using secret:', secret.substring(0, 10) + '...');

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
    });
  }

  async validate(payload: JwtPayload) {
    const user = await this.authService.validateJwtPayload(payload);
    if (!user) {
      throw new UnauthorizedException();
    }

    // Determine if user is staff based on roles
    const staffRoles = ['admin', 'administrator', 'staff', 'moderator', 'manager'];
    const isStaff = payload.roles?.some(role => 
      staffRoles.includes(role.toLowerCase())
    ) || false;

    // Return consistent user object format for audit trail and other services
    return {
      user_id: payload.sub,
      userId: payload.sub, // Keep for backward compatibility
      email: payload.email,
      roles: payload.roles || [],
      isStaff: isStaff,
      first_name: user.first_name,
      last_name: user.last_name,
    };
  }
}
