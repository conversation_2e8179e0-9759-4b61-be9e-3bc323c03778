import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Applications } from '../entities/applications.entity';
import { ApplicationStatusHistory } from '../entities/application-status-history.entity';
import { User } from '../entities/user.entity';
import { ApplicationStatusTrackingController } from '../controllers/application-status-tracking.controller';
import { ApplicationStatusTrackingService } from '../services/application-status-tracking.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Applications,
      ApplicationStatusHistory,
      User
    ])
  ],
  controllers: [ApplicationStatusTrackingController],
  providers: [ApplicationStatusTrackingService],
  exports: [ApplicationStatusTrackingService]
})
export class ApplicationStatusTrackingModule {}
