import { IsString, IsEnum, IsOptional, IsUUID, IsInt, IsDateString, Min, Max, Matches } from 'class-validator';
import { ApplicationStatus } from '../../entities/applications.entity';

export class CreateApplicationDto {
  @IsString()
  application_number: string;

  @IsUUID()
  applicant_id: string;

  @IsUUID()
  license_category_id: string;

  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(6)
  current_step?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  progress_percentage?: number;

  @IsOptional()
  @IsDateString()
  submitted_at?: string;
}
