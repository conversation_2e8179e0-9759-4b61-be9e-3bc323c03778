import { IsString, IsEnum, <PERSON>Optional, IsUUID, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { TaskStatus, TaskPriority } from '../../entities/tasks.entity';

export class UpdateTaskDto {
  @ApiProperty({ description: 'Task title', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Task description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: TaskStatus, description: 'Task status', required: false })
  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus;

  @ApiProperty({ enum: TaskPriority, description: 'Task priority', required: false })
  @IsEnum(TaskPriority)
  @IsOptional()
  priority?: TaskPriority;

  @ApiProperty({ description: 'User ID to assign task to', required: false })
  @IsUUID()
  @IsOptional()
  assigned_to?: string;

  @ApiProperty({ description: 'Due date for task completion', required: false })
  @IsDateString()
  @IsOptional()
  due_date?: string;

  @ApiProperty({ description: 'Review notes', required: false })
  @IsString()
  @IsOptional()
  review_notes?: string;

  @ApiProperty({ description: 'Completion notes', required: false })
  @IsString()
  @IsOptional()
  completion_notes?: string;
}