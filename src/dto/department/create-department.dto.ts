// create-department.dto.ts
import { IsNotEmpty, IsOptional, IsString, Length, IsEmail } from 'class-validator';

export class CreateDepartmentDto {
  @IsNotEmpty({ message: 'Short departmental code is required' })
  @IsString()
  @Length(1, 5)
  code: string;

  @IsNotEmpty()
  @IsString()
  @Length(1, 100)
  name: string;

  @IsNotEmpty({ message: 'Description is required' })
  @IsString()
  description: string;

  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail()
  email: string;

  @IsOptional()
  created_by?: string; // uuid string of creator
}
